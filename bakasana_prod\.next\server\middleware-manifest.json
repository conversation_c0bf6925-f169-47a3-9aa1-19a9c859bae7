{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/api/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/admin(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/admin/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "VCh_o2TCqtBYDxB6OGu-t", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "pC+gLU7N0gPM+hL33F4gA5grgGqY36LSmSskWwyj33U=", "__NEXT_PREVIEW_MODE_ID": "c45beb038868c5d957ad78ce13b6ff3f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a11cd9927cda761fdca838d85acd99c05067751242cd0adb9f752d9caaee30d5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "95f3eb5cee4a54d8872a9e86fdcf9fee7bbe33cd8930d79543b5837ed1184c13"}}}, "functions": {}, "sortedMiddleware": ["/"]}